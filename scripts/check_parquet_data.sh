#!/bin/bash

# Script to check Simple Parquet data in LocalStack S3

echo "🔍 Checking Simple Parquet data storage..."
echo ""

# Check if LocalStack is running
if ! curl -s http://localhost:4566/health > /dev/null; then
    echo "❌ LocalStack is not running. Please start it with: docker-compose up -d"
    exit 1
fi

echo "✅ LocalStack is running"
echo ""

echo "📁 All Parquet Files Found:"

# Show all files in S3
curl -s "http://localhost:4566/mock-bucket?list-type=2" | \
grep -o '<Key>[^<]*</Key>' | \
sed 's/<Key>//g; s/<\/Key>//g' | \
sort | \
while read -r key; do
    if [ ! -z "$key" ]; then
        if [[ $key == *".parquet" ]]; then
            echo "  📄 s3://mock-bucket/$key "
        elif [[ $key == *"_SUCCESS" ]]; then
            echo "  ✅ s3://mock-bucket/$key "
        elif [[ $key == *"mysql"* ]] || [[ $key == *"mssql"* ]]; then
            echo "  📂 s3://mock-bucket/$key"
        fi
    fi
done
