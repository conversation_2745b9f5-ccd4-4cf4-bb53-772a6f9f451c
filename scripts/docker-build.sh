#!/bin/bash

# Docker Build Script for Spark Metadata-Driven ETL Pipeline
# This script builds the Docker image for the Spark pipeline

set -e

echo "🐳 Building Spark Metadata-Driven ETL Pipeline Docker Image"
echo "============================================================"

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

# Build the Docker image
echo "📦 Building Docker image..."
docker build -t spark-metadata-pipeline:latest .

echo ""
echo "✅ Docker image built successfully!"
echo ""
echo "🏷️  Image: spark-metadata-pipeline:latest"
echo ""
echo "🚀 Next steps:"
echo "   • Run with MySQL:      ./scripts/docker-run-mysql.sh"
echo "   • Run with SQL Server: ./scripts/docker-run-mssql.sh"
echo "   • Run with PostgreSQL: ./scripts/docker-run-postgres.sh"
echo "   • Or use Docker Compose: docker-compose up"
