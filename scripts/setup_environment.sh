#!/bin/bash

# Spark Metadata-Driven Pipeline Environment Setup
# This script sets all required environment variables

echo "🔧 Setting up environment variables for Spark Metadata-Driven Pipeline..."

# Configuration paths (using local resources for development)
export CONFIG_ROOT_DIR="src/main/resources"
export CONFIG_ENDPOINTS_DIR="src/main/resources/endpoints"
export CONFIG_JOBS_DIR="src/main/resources/jobs"
export EXTRA_JARS_FOLDER="src/main/resources/jars"

# AWS credentials (for LocalStack development)
export AWS_ACCESS_KEY_ID="test"
export AWS_SECRET_ACCESS_KEY="test"
export AWS_DEFAULT_REGION="us-east-1"

# S3 endpoint (LocalStack for local development)
export S3_ENDPOINT="http://localhost:4566"

# Application settings
export APP_NAME_PREFIX="spark-metadata-pipeline"
export LOG_LEVEL="INFO"

# Additional Spark settings
export S3_PATH_STYLE_ACCESS="true"
