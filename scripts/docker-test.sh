#!/bin/bash

# Docker Test Script
# This script tests the Docker setup by building the image and running a quick test

set -e

echo "🧪 Testing Docker Setup for Spark Metadata-Driven ETL Pipeline"
echo "=============================================================="

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

echo "📦 Step 1: Building Docker image..."
docker build -t spark-metadata-pipeline:test .

echo ""
echo "🔍 Step 2: Testing image structure..."
docker run --rm spark-metadata-pipeline:test --help

echo ""
echo "📋 Step 3: Checking JAR files in image..."
docker run --rm spark-metadata-pipeline:test ls -la /app/src/main/resources/jars/

echo ""
echo "🐍 Step 4: Testing Python dependencies..."
docker run --rm spark-metadata-pipeline:test python3 -c "import pyspark; print(f'PySpark version: {pyspark.__version__}')"

echo ""
echo "☕ Step 5: Testing Java/Spark installation..."
docker run --rm spark-metadata-pipeline:test java -version

echo ""
echo "✅ Docker setup test completed successfully!"
echo ""
echo "🚀 Ready to run:"
echo "   • ./scripts/docker-run-mysql.sh"
echo "   • ./scripts/docker-run-mssql.sh"
echo "   • ./scripts/docker-run-postgres.sh"

# Clean up test image
docker rmi spark-metadata-pipeline:test
