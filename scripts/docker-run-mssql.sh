#!/bin/bash

# Docker Run Script for SQL Server Configuration
# This script runs the Spark pipeline with SQL Server as the input database

set -e

echo "🐳 Running Spark Pipeline with SQL Server Configuration"
echo "======================================================"

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

# Create outputs directory if it doesn't exist
mkdir -p outputs

echo "🚀 Starting services with SQL Server configuration..."

# Start all services including SQL Server
docker-compose -f docker-compose.yml -f docker-compose.mssql.yml up --build

echo ""
echo "✅ Pipeline execution completed!"
echo ""
echo "📁 Check outputs in: ./outputs/"
echo "🔍 View logs with: docker-compose logs spark-pipeline"
