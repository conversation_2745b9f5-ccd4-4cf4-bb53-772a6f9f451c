#!/bin/bash

# Standalone Docker Run Script
# This script runs the Spark pipeline as a standalone container with configurable database input

set -e

# Default values
DB_TYPE="mysql"
CONFIG_FILE="sample_mysql_job.yaml"
NETWORK_NAME="spark-metadata-driven_mynetwork"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --db-type)
      DB_TYPE="$2"
      shift 2
      ;;
    --config-file)
      CONFIG_FILE="$2"
      shift 2
      ;;
    --network)
      NETWORK_NAME="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo ""
      echo "Options:"
      echo "  --db-type TYPE      Database type (mysql, mssql, postgres) [default: mysql]"
      echo "  --config-file FILE  Configuration file name [default: sample_mysql_job.yaml]"
      echo "  --network NAME      Docker network name [default: spark-metadata-driven_mynetwork]"
      echo "  --help              Show this help message"
      echo ""
      echo "Examples:"
      echo "  $0 --db-type mysql"
      echo "  $0 --db-type mssql --config-file sample_mssql_job.yaml"
      echo "  $0 --db-type postgres --config-file sample_postgres_job.yaml"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

echo "🐳 Running Spark Pipeline Standalone Container"
echo "=============================================="
echo "Database Type: $DB_TYPE"
echo "Config File: $CONFIG_FILE"
echo "Network: $NETWORK_NAME"
echo ""

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

# Create outputs directory if it doesn't exist
mkdir -p outputs

# Set environment file based on database type
ENV_FILE="configs/.env.$DB_TYPE"

if [ ! -f "$ENV_FILE" ]; then
    echo "❌ Environment file not found: $ENV_FILE"
    echo "Available options: mysql, mssql, postgres"
    exit 1
fi

echo "📋 Using environment file: $ENV_FILE"

# Run the container
docker run --rm \
    --name spark-pipeline-standalone \
    --network "$NETWORK_NAME" \
    --env-file "$ENV_FILE" \
    -v "$(pwd)/configs:/app/configs" \
    -v "$(pwd)/outputs:/app/outputs" \
    spark-metadata-pipeline:latest \
    --config_file "$CONFIG_FILE" \
    --app_type batch \
    --config_root_dir /app/configs \
    --s3_type localstack \
    --extra_jars_folder /app/src/main/resources/jars

echo ""
echo "✅ Pipeline execution completed!"
echo ""
echo "📁 Check outputs in: ./outputs/"
