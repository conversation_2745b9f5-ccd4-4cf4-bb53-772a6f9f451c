#!/bin/bash

# Docker Run Script for MySQL Configuration
# This script runs the Spark pipeline with MySQL as the input database

set -e

echo "🐳 Running Spark Pipeline with MySQL Configuration"
echo "=================================================="

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

# Create outputs directory if it doesn't exist
mkdir -p outputs

echo "🚀 Starting services with MySQL configuration..."

# Start all services including MySQL
docker-compose -f docker-compose.yml -f docker-compose.mysql.yml up --build

echo ""
echo "✅ Pipeline execution completed!"
echo ""
echo "📁 Check outputs in: ./outputs/"
echo "🔍 View logs with: docker-compose logs spark-pipeline"
