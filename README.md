# Simple Database to Parquet Pipeline

Read data from MySQL/SQL Server databases and write to S3 as Parquet files.

## 🎯 What It Does

- **Reads**: MySQL and SQL Server customer data
- **Transforms**: Adds age_range column (young/mature)
- **Writes**: Parquet files to S3 with Snappy compression
- **Partitions**: By age_range for better performance

## 🚀 Quick Start

```bash
# 1. Download dependencies
./scripts/download_jars.sh

# 2. Start services
source scripts/setup_environment.sh && docker-compose up -d

# 3. Setup databases with sample data
./scripts/setup_databases.sh

# 4. Run pipeline (choose one)
./scripts/run_pipeline.sh iceberg

# OR run manually
  python3 src/main/pipeline_main.py \
    --config_file job.iceberg.sample.yaml \
    --config_source_type local \
    --config_root_dir src/main/resources \
    --app_type batch \
    --s3_type localstack \
    --s3_url http://localhost:4566 \
    --extra_jars_folder src/main/resources/jars

# 5. Check structed file:
brew install awscli
aws --endpoint-url=http://localhost:4566 s3api list-objects-v2 \
  --bucket mock-bucket \
  --query 'Contents[?ends_with(Key, `.parquet`) || ends_with(Key, `_SUCCESS`) || contains(Key, `mysql`) || contains(Key, `mssql`)] | [].{Key: Key, LastModified: LastModified}' \
  --output table

# OR
./scripts/check_parquet_data.sh

# 6. Check results - view your data
python3 scripts/show_data.py
```

## 🐳 Docker Deployment

### Quick Docker Start

Choose your database input and run:

```bash
# MySQL Input
./scripts/docker-run-mysql.sh

# SQL Server Input
./scripts/docker-run-mssql.sh

# PostgreSQL Input
./scripts/docker-run-postgres.sh
```

### Manual Docker Build & Run

```bash
# 1. Build the Docker image
./scripts/docker-build.sh

# 2. Run with specific database (standalone)
./scripts/docker-run-standalone.sh --db-type mysql
./scripts/docker-run-standalone.sh --db-type mssql
./scripts/docker-run-standalone.sh --db-type postgres

# 3. Check outputs
ls -la outputs/
```

### Docker Compose Options

```bash
# Default (shows help)
docker-compose up

# MySQL configuration
docker-compose -f docker-compose.yml -f docker-compose.mysql.yml up

# SQL Server configuration
docker-compose -f docker-compose.yml -f docker-compose.mssql.yml up

# PostgreSQL configuration
docker-compose -f docker-compose.yml -f docker-compose.postgres.yml up
```

### Environment Configuration

Each database type has its own environment file:

- `configs/.env.mysql` - MySQL database settings
- `configs/.env.mssql` - SQL Server database settings
- `configs/.env.postgres` - PostgreSQL database settings

**Example MySQL Environment:**
```bash
DB_TYPE=mysql
DB_HOST=mysql
DB_PORT=3306
DB_USER=test
DB_PASSWORD=123456
S3_ENDPOINT=http://localstack:4566
OUTPUT_PATH=/app/outputs
```

### Custom Configuration

Create your own job configuration:

```bash
# 1. Copy sample config
cp configs/jobs/sample_mysql_job.yaml configs/jobs/my_custom_job.yaml

# 2. Edit the configuration
# 3. Run with custom config
docker run --rm \
  --network spark-metadata-driven_mynetwork \
  --env-file configs/.env.mysql \
  -v $(pwd)/configs:/app/configs \
  -v $(pwd)/outputs:/app/outputs \
  spark-metadata-pipeline:latest \
  --config_file my_custom_job.yaml \
  --app_type batch \
  --config_root_dir /app/configs \
  --s3_type localstack \
  --extra_jars_folder /app/src/main/resources/jars
```

## 📁 Output Structure

Your data is saved as Parquet files in S3:

```
s3://mock-bucket/
├── mysql_customers/
│   └── age_range=young/
│       └── part-*.snappy.parquet    # MySQL customer data
└── mssql_customers/
    └── age_range=mature/
        └── part-*.snappy.parquet    # SQL Server customer data
```

**Docker Output Structure:**
```
outputs/
├── mysql_data/
│   └── part-*.parquet              # MySQL data output
├── mssql_data/
│   └── part-*.parquet              # SQL Server data output
└── postgres_data/
    └── part-*.parquet              # PostgreSQL data output
```

**Partition Field**: `age_range` (young/mature based on age 20-40)

## ⚙️ Configuration

Simple job configuration in `src/main/resources/jobs/job.iceberg.sample.yaml`:

```yaml
jobs:
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mysql_customers
      partition_columns:
        - age_range
```

## 🔧 Helper Scripts

### Local Development Scripts
- `download_jars.sh` - Download required JAR files
- `setup_environment.sh` - Set environment variables
- `setup_databases.sh` - Create databases and sample data
- `run_pipeline.sh iceberg` - Run the pipeline
- `show_data.py` - View your data as clean dataframes

### Docker Scripts
- `docker-build.sh` - Build the Docker image
- `docker-run-mysql.sh` - Run pipeline with MySQL input
- `docker-run-mssql.sh` - Run pipeline with SQL Server input
- `docker-run-postgres.sh` - Run pipeline with PostgreSQL input
- `docker-run-standalone.sh` - Run standalone container with options

## 📊 Data Flow

1. **MySQL/SQL Server** → Read customer data
2. **Transform** → Add age_range column (young: 20-40, mature: >40)
3. **Partition** → Group by age_range
4. **Write** → Save as Parquet files to S3

## 🗂️ Project Structure

```
spark-metadata-driven/
├── scripts/                     # Helper scripts
│   ├── docker-build.sh         # Build Docker image
│   ├── docker-run-mysql.sh     # Run with MySQL
│   ├── docker-run-mssql.sh     # Run with SQL Server
│   ├── docker-run-postgres.sh  # Run with PostgreSQL
│   └── docker-run-standalone.sh # Standalone container
├── configs/                     # Docker configurations
│   ├── .env.mysql              # MySQL environment
│   ├── .env.mssql              # SQL Server environment
│   ├── .env.postgres           # PostgreSQL environment
│   ├── jobs/                   # Job configurations
│   │   ├── sample_mysql_job.yaml
│   │   ├── sample_mssql_job.yaml
│   │   └── sample_postgres_job.yaml
│   └── endpoints/              # Database endpoints
├── src/main/
│   ├── writer/
│   │   └── iceberg_writer.py    # Simple Parquet writer
│   ├── resources/
│   │   ├── jobs/
│   │   │   └── job.iceberg.sample.yaml
│   │   └── jars/               # Downloaded JAR files
├── outputs/                    # Docker output directory
├── Dockerfile                  # Docker image definition
├── docker-compose.yml          # Base services
├── docker-compose.mysql.yml    # MySQL override
├── docker-compose.mssql.yml    # SQL Server override
└── docker-compose.postgres.yml # PostgreSQL override
```

## ✅ What You Get

- **Clean Parquet files** with Snappy compression
- **Partitioned data** for better query performance  
- **Simple S3 structure** - easy to understand
- **No complex metadata** - just pure Parquet files
- **Automated scripts** - easy to run and verify

Perfect for: MySQL/SQL Server → S3 Parquet data migration

## 📋 Sample Data

The `setup_databases.sh` script creates this sample data:

**MySQL & SQL Server customers table:**
```
+----+---------------+-----+---------------------+
| id | name          | age | updated_at          |
+----+---------------+-----+---------------------+
|  1 | John Doe      |  25 | 2025-01-03 12:00:00 |
|  2 | Jane Smith    |  35 | 2025-01-03 12:00:00 |
|  3 | Bob Johnson   |  45 | 2025-01-03 12:00:00 |
|  4 | Alice Brown   |  28 | 2025-01-03 12:00:00 |
|  5 | Charlie Wilson|  52 | 2025-01-03 12:00:00 |
+----+---------------+-----+---------------------+
```

**After pipeline transformation:**
- Ages 20-40 → `age_range = 'young'` (John, Jane, Alice)
- Ages >40 → `age_range = 'mature'` (Bob, Charlie)
