import pytest
from config.transformer import WithColumnTransformerConfig, Column
from transformer.withcolumn_transformer import WithColumnTransformer

class TestTransformer:
    def test_with_column(self, spark_setup):
        data = [(1, "<PERSON>", 30), (2, "<PERSON>", 40), (3, "<PERSON>", 50)]
        columns = ["id", "name", "age"]
        df = spark_setup.createDataFrame(data, columns)
        new_col1 = Column(colname="range",expr="CASE WHEN age between 20 and 40 THEN 1 ELSE 2 END")
        list_col = []
        list_col.append(new_col1)
        config = WithColumnTransformerConfig(columns=list_col)
        transformer = WithColumnTransformer(config)
        transformer_df = transformer.transform(df) 
        filter_df = transformer_df.filter("range=2")
        assert filter_df.count() == 1