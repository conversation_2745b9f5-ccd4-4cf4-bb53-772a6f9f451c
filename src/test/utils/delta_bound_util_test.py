import pytest
from utils.delta_bound_util import get_lowerbound, get_upperbound, QueryCondition, JdbcConfig
import os

class TestDeltaBoundUtil:
    def test_lower_bound_retrieve_postgre_first_time(self,postgresql_create_control_table):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        qc = QueryCondition("system_name","schema_name","table_name")
        endpoint_path = "internal.endpoint.yaml"
        lb = get_lowerbound(qc,endpoint_path)
        assert lb == "1970-01-01 00:00:00" 
        
    def test_lower_bound_retrieve_postgre_second_time(self,postgresql_insert_control_table):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        qc = QueryCondition("system_name","schema_name","table_name")
        endpoint_path = "internal.endpoint.yaml"
        lb = get_lowerbound(qc,endpoint_path)
        assert lb == "2025-01-01 00:15:00" 
        
    def test_upper_bound_retrieve_mysql(self,mysql_setup):
        config = JdbcConfig("mysql","test","localhost",3306,"empty","test","customers","test","123456",None)
        ub = get_upperbound(config,"updated_at")
        assert ub == "2025-03-01 00:10:01"