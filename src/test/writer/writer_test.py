import pytest
from config.writer import PostgresWriterConfig, S3WriterConfig, JsonlWriterFormatConfig
from writer.postgre_writer import PostgreWriter
from writer.s3_writer import S3Writer
from transformer.withcolumn_transformer import WithColumnTransformer
import os


class TestWriter:
    def test_write_to_postgresql(self, spark_setup):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        data = [(1, "Alice", 30), (2, "<PERSON>", 40), (3, "<PERSON>", 50)]
        columns = ["id", "name", "age"]
        endpoint = "postgresql.endpoint.yaml"
        df = spark_setup.createDataFrame(data, columns)
        config = PostgresWriterConfig(
            type="PostgresWriter", endpoint=endpoint, table="customers", mode="overwrite")
        writer = PostgreWriter(config, None)
        writer.write(df)
        assert df.count() == 3

    def test_write_to_s3(self, spark_setup, s3_localstack):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        data = [(1, "<PERSON>", 30), (2, "<PERSON>", 40), (3, "Tommy", 50)]
        columns = ["id", "name", "age"]
        endpoint = "s3.localstack.endpoint.yaml"
        df = spark_setup.createDataFrame(data, columns)
        format_config = JsonlWriterFormatConfig(
            type="JsonWriterFormat", source_options=None)
        config = S3WriterConfig(type="S3Writer", endpoint=endpoint, key="outbound/json_dummy",
                                mode="overwrite", num_files=1, format=format_config)
        writer = S3Writer(
            config=config, process_date="2025-01-01", custom_options=None)
        writer.write(df)
        assert df.count() == 3
