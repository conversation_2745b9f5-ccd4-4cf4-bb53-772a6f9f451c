from config.configparser import load_yaml
from config.endpoint import MysqlEndpoint, InternalDBEndpoint, MssqlEndpoint, S3Endpoint
from utils.file_util import read_file


class TestConfigEndpoint:
    def test_config_mysql_endpoint(self):
        file_path = "src/test/resources/mysql.endpoint.yaml"
        string_yaml = read_file(file_path)
        config = load_yaml(string_yaml, MysqlEndpoint)
        assert isinstance(config, MysqlEndpoint)
        assert config.endpoint_type == "mysql"
        assert config.system == "test"
        assert config.host == "localhost"
        assert config.port == 3306
        assert config.schema_ == "test"
        assert config.authentication.credential.username == "test"
        assert config.authentication.credential.password == "123456"

    def test_config_mssql_endpoint(self):
        file_path = "src/test/resources/mssql.endpoint.yaml"
        string_yaml = read_file(file_path)
        config = load_yaml(string_yaml, MssqlEndpoint)
        assert isinstance(config, MssqlEndpoint)
        assert config.endpoint_type == "mssql"
        assert config.system == "test"
        assert config.host == "localhost"
        assert config.port == 1433
        assert config.schema_ == "dbo"
        assert config.authentication.credential.username == "sa"
        assert config.authentication.credential.password == "123456aA"

    def test_config_internal_endpoint(self):
        file_path = "src/test/resources/internal.endpoint.yaml"
        string_yaml = read_file(file_path)
        config = load_yaml(string_yaml, InternalDBEndpoint)
        assert isinstance(config, InternalDBEndpoint)
        assert config.endpoint_type == "postgresql"
        assert config.database == "test"
        assert config.host == "localhost"
        assert config.port == 5432
        assert config.schema_ == "public"
        assert config.authentication.credential.username == "postgres"
        assert config.authentication.credential.password == "123456"
        assert config.control_table == "job_logger"
        assert config.system_name_column == "system_name"
        assert config.schema_name_column == "schema_name"
        assert config.table_name_column == "table_name"

    def test_s3_endpoint(self):
        file_path = "src/test/resources/s3.localstack.endpoint.yaml"
        string_yaml = read_file(file_path)
        config = load_yaml(string_yaml, S3Endpoint)
        assert config.bucket == "mock-bucket"
        assert config.authentication.credential.access_key_id == "test"
        assert config.authentication.credential.secret_access_key == "test"
        assert config.authentication.credential.session_token == "test"
