from dataclasses import dataclass
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Row
from config.endpoint import InternalDBEndpoint
from config.configparser import load_yaml
from utils import file_util
from utils.constants import INTERNAL_PIPELINE_ENGINE_ENDPOINT
from reader.jdbc import JdbcConnect
from reader.reader import JdbcConfig
import os


@dataclass
class ValueSet:
    system: str
    schema: str
    table: str
    lower_bound: str
    upper_bound: str
    count: int


@dataclass
class QueryCondition:
    system: str
    schema: str
    table: str


def append(
    value: ValueSet,
    endpoint_path: str = f"{INTERNAL_PIPELINE_ENGINE_ENDPOINT}.yaml",
):
    db_config = __get_internal_endpoint(endpoint_path)
    if db_config.endpoint_type == "postgresql":
        __append_postgre(value, db_config)


def __get_internal_endpoint(path: str):
    endpoint_root_dir = os.getenv("CONFIG_ENDPOINTS_DIR")
    if endpoint_root_dir:
        full_path = f"{endpoint_root_dir}/{path}"
    else:
        full_path = path
    db = load_yaml(file_util.read_file(full_path), InternalDBEndpoint)
    return db


def __append_postgre(value: ValueSet, db_config: InternalDBEndpoint):
    # connect to postgresql
    engine = create_engine(
        f"postgresql+psycopg://{db_config.authentication.credential.username}:{db_config.authentication.credential.password}@{db_config.host}/{db_config.database}"
    )

    with engine.connect() as internal_conn:
        # Execute query
        internal_conn.execute(
            text(
                f"INSERT INTO {db_config.database}.{db_config.schema_}.{db_config.control_table} ({db_config.system_name_column},{db_config.schema_name_column},{db_config.table_name_column}, lower_bound, upper_bound, record_count) VALUES (:1, :2, :3, :4, :5, :6)"
            ),
            {
                "1": value.system,
                "2": value.schema,
                "3": value.table,
                "4": value.lower_bound,
                "5": value.upper_bound,
                "6": value.count,
            },
        )
        internal_conn.commit()         # type: ignore
        internal_conn.close()


# type: ignore
def get_lowerbound(qc: QueryCondition, endpoint_path: str = f"{INTERNAL_PIPELINE_ENGINE_ENDPOINT}.yaml"):
    db_config = __get_internal_endpoint(endpoint_path)
    lowerbound = "1970-01-01 00:00:00"
    # connect to postgresql
    engine = create_engine(
        f"postgresql+psycopg://{db_config.authentication.credential.username}:{db_config.authentication.credential.password}@{db_config.host}/{db_config.database}"
    )
    with engine.connect() as internal_conn:
        # Execute query
        result = internal_conn.execute(
            text(
                f"""SELECT upper_bound
                    FROM {db_config.database}.{db_config.schema_}.{db_config.control_table}
                    WHERE {db_config.system_name_column} = :system_name
                    AND {db_config.schema_name_column} = :schema_name
                    AND {db_config.table_name_column} = :table_name
                    ORDER BY id DESC
                    LIMIT 100
                """
            ),
            {"system_name": qc.system,
             "schema_name": qc.schema,
             "table_name": qc.table
             }
        ).first()
        if result is not None:
            lowerbound = result[0].strftime("%Y-%m-%d %H:%M:%S")
        internal_conn.close()
    return lowerbound


def get_upperbound(conn_props: JdbcConfig, delta_column):
    upperbound = "1970-01-01 00:00:00"
    jdbc_conn = JdbcConnect(conn_props.type, conn_props.username,
                            conn_props.password, conn_props.host, conn_props.system)
    query = f"SELECT MAX({delta_column}) FROM {conn_props.schema}.{conn_props.table}"
    print(query)
    result = jdbc_conn.run_query(query)
    if result is not None:
        upperbound = result[0].strftime("%Y-%m-%d %H:%M:%S")

    return upperbound
