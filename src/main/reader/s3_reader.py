from reader.reader import Reader
from config.reader import S3<PERSON>eaderConfig,DSVReaderFormatConfig,AvroReaderFormatConfig,ParquetReaderFormatConfig,XMLReaderFormatConfig,ORCReaderFormatConfig,ExcelReaderFormatConfig,JsonReaderFormatConfig
from typing import Optional,Dict
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField
import logging
import json


class S3Reader(Reader):
    def __init__(self, config: S3ReaderConfig, options: Optional[Dict[str,str]],num_partitions: int =1):
        self.config = config
        self.options= options
        self.num_partitions = num_partitions     
        self.endpoint = config.endpoint     
        
    def read(self, spark: SparkSession):
        if isinstance(self.config.format,DSVReaderFormatConfig):
            source = "csv"
        elif isinstance(self.config.format,AvroReaderFormatConfig):
            source = "avro"
        elif isinstance(self.config.format,ParquetReaderFormatConfig):
            source = "parquet"
        elif isinstance(self.config.format,XMLReaderFormatConfig):
            source = "com.databricks.spark.xml"
        elif isinstance(self.config.format,ORCReaderFormatConfig):
            source = "orc"
        elif isinstance(self.config.format,ExcelReaderFormatConfig):
            source = "excel"
        elif isinstance(self.config.format,JsonReaderFormatConfig):
            source = "json"
        else:
            raise ValueError(f"Unsupported type of file {self.config.format}")
        options = self.config.format.source_options or dict()      
        key = options.get("s3.reader.key", self.config.key) # type: ignore     
        header_key =  options.get("s3.reader.header_key", "") # type: ignore     
        endpoint_config = self.config.endpoint_config
        # check if authentication is defined
        if endpoint_config.authentication is not None: # type: ignore
            creds = endpoint_config.authentication.credential # type: ignore
            hadoop_config = spark.sparkContext._jsc.hadoopConfiguration() # type: ignore
            hadoop_config.set("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem") # type: ignore
            #hadoop_config.set("spark.hadoop.fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider") # type: ignore
            hadoop_config.set("spark.hadoop.fs.s3a.access.key", creds.access_key_id) # type: ignore
            hadoop_config.set("spark.hadoop.fs.s3a.secret.key", creds.secret_access_key) # type: ignore
            # hadoop_config.set("spark.hadoop.fs.s3a.session.token", creds.session_token) # type: ignore
        path = f"s3a://{endpoint_config.bucket}/{key}" # type: ignore
        predicate = options.get("s3.reader.filter", "1 = 1")
        logging.info(f"Read data from S3 path: {path} with predicate {predicate}")
        if self.config.schema_ is not None:
            if (source != "json" ):
                df = spark.read.format(source).options(**options).schema(self.config.schema_).load(path).filter(predicate).repartition(self.num_partitions)
            else:
                json_schema = StructType.fromJson(json.loads(self.config.schema_))
                df = spark.read.schema(json_schema).options(**options).json(path).filter(predicate).repartition(self.num_partitions)
        else:
            if (header_key != ""):
                if (source != "json" ):
                    header_path = f"s3a://{endpoint_config.bucket}/{header_key}" # type: ignore
                    options["header"]= "true"                    
                    header_df = spark.read.format(source).options(**options).load(header_path).filter(predicate)
                    columns = header_df.columns
                    header_fields = sorted(header_df.schema.fields, key=lambda field: field.name)                    
                    logging.info(f"Read header from S3 path {header_path}")       
                    options["header"]= "false"                  
                    df = spark.read.format(source).options(**options).load(path).filter(predicate).toDF(*columns).repartition(self.num_partitions)
                else:
                    df = spark.read.options(**options).json(path).filter(predicate).repartition(self.num_partitions)
            else:
                if (source != "json" ):
                    df = spark.read.options(**options).format(source).load(path).filter(predicate).repartition(self.num_partitions)
                else:                        
                    df = spark.read.options(**options).json(path).filter(predicate).repartition(self.num_partitions)
        return df # type: ignore