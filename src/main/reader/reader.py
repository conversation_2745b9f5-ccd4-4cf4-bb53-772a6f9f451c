import logging
from pyspark.sql.dataframe import DataFrame
from pyspark.sql import SparkSession
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional, Dict
from pydantic import BaseModel

class Reader(ABC):
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    def read(self, spark: SparkSession) -> DataFrame:
        pass


@dataclass
class JdbcConfig:
    type: str
    system: str
    host: str
    port: int
    database: str
    schema: str
    table: str
    username: str
    password: str
    connection_properties: Optional[Dict[str,str]]