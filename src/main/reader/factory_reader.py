from config.reader import Reader<PERSON>onfig, Mysql<PERSON><PERSON><PERSON><PERSON>onfig, MssqlReaderConfig, S3ReaderConfig
from reader.reader import Reader
from reader.mysql_reader import MysqlReader
from reader.mssql_reader import MssqlReader
from reader.s3_reader import S3Reader
from utils.constants import INTERNAL_PIPELINE_ENGINE_ENDPOINT
from utils.commons import JobParams


class ReaderFactory:
    @staticmethod
    def create(config: Reader, params: JobParams, custom_options: dict[str, str]) -> Reader:
        internal_endpoint_path = f"{INTERNAL_PIPELINE_ENGINE_ENDPOINT}.yaml"
        if isinstance(config, MysqlReaderConfig):
            return MysqlReader(config, custom_options, internal_endpoint_path, params.batch_size)
        elif isinstance(config, MssqlReaderConfig):
            return MssqlReader(config, custom_options, internal_endpoint_path, params.batch_size)
        elif isinstance(config, S3ReaderConfig):
            return S3Reader(config, custom_options)
        else:
            raise ValueError("Unsupported type of reader")
