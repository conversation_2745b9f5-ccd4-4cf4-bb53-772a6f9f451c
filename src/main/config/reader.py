from typing import Optional,Literal,Union
from config.endpoint import *
from utils import file_util
from config.configparser import load_yaml
from pydantic import BaseModel, model_validator
import os

class ReaderFormatConfig(BaseModel):
    type: Literal["DSVReaderFormat", "ParquetReaderFormat", "AvroReaderFormat", "XMLReaderFormat", "ORCReaderFormat", "ExcelReaderFormat", "JsonReaderFormat"]
    source_options: Optional[dict[str,str]] = None
    
class DSVReaderFormatConfig(ReaderFormatConfig):
    type: Literal["DSVReaderFormat"] # type: ignore
    source_options: Optional[dict[str,str]] = None
    
class ParquetReaderFormatConfig(ReaderFormatConfig):
    type: Literal["ParquetReaderFormat"] # type: ignore
    source_options: Optional[dict[str,str]] = None
    
class AvroReaderFormatConfig(ReaderFormatConfig):
    type: Literal["AvroReaderFormat"] # type: ignore
    source_options: Optional[dict[str,str]] = None
    
class XMLReaderFormatConfig(ReaderFormatConfig):
    type: Literal["XMLReaderFormat"] # type: ignore
    source_options: Optional[dict[str,str]] = None
    
class ORCReaderFormatConfig(ReaderFormatConfig):
    type: Literal["ORCReaderFormat"] # type: ignore
    source_options: Optional[dict[str,str]] = None
    
class ExcelReaderFormatConfig(ReaderFormatConfig):
    type: Literal["ExcelReaderFormat"] # type: ignore
    source_options: Optional[dict[str,str]] = None
    
class JsonReaderFormatConfig(ReaderFormatConfig):
    type: Literal["JsonReaderFormat"] # type: ignore
    source_options: Optional[dict[str,str]] = None

class ReaderConfig(BaseModel):
    type: Literal["MssqlReader", "MysqlReader", "PostgresReader", "S3Reader"]
    endpoint: str
    endpoint_config: Optional[BaseEndpoint] = None

    @model_validator(mode="after")
    def set_endpoint_config(self):
        """Loads endpoint config from YAML."""
        endpoint_mapping = {
            "MssqlReader": MssqlEndpoint,
            "MysqlReader": MysqlEndpoint,
            "PostgresReader": PostgresEndpoint,
            "S3Reader": S3Endpoint
        }

        model = endpoint_mapping.get(self.type)
        if model is None:
            raise ValueError(f"Unknown reader type: {self.type}")

        endpoint_root_dir = os.getenv("CONFIG_ENDPOINTS_DIR")
        if not endpoint_root_dir:
            raise ValueError("CONFIG_ENDPOINTS_DIR environment variable is not set")

        # Initialize `endpoint_config` by parsing the YAML file
        self.endpoint_config = load_yaml(file_util.read_file(f"{endpoint_root_dir}/{self.endpoint}"), model)
        return self


class MysqlReaderConfig(ReaderConfig):
    type: Literal["MysqlReader"] # type: ignore
    table: str
    strategy: str
    delta_column: Optional[str] = None
    num_partitions: int = 1   

class MssqlReaderConfig(ReaderConfig):   
    type: Literal["MssqlReader"] # type: ignore
    table: str
    strategy: str
    delta_column: Optional[str] = None
    num_partitions: int = 1    

class PostgresReaderConfig(ReaderConfig):
    type: Literal["PostgresReader"] # type: ignore
    table: str
    strategy: str
    delta_column: Optional[str] = None
    num_partitions: int = 1    
    
READER_FORMAT_UNION = Union[DSVReaderFormatConfig,ParquetReaderFormatConfig,AvroReaderFormatConfig,XMLReaderFormatConfig,ORCReaderFormatConfig,ExcelReaderFormatConfig,JsonReaderFormatConfig]

class S3ReaderConfig(ReaderConfig):
    type: Literal["S3Reader"] # type: ignore
    key: str
    format: READER_FORMAT_UNION
    schema_: Optional[str] = None

 
