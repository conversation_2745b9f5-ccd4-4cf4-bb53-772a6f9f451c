"""
Environment configuration management for AWS EMR deployment
"""
import os
from typing import Optional
from pydantic import BaseModel


class EnvironmentConfig(BaseModel):
    """Environment configuration for the pipeline"""

    # AWS Configuration
    aws_region: str = os.getenv("AWS_DEFAULT_REGION", "us-east-1")
    aws_access_key_id: Optional[str] = os.getenv("AWS_ACCESS_KEY_ID")
    aws_secret_access_key: Optional[str] = os.getenv("AWS_SECRET_ACCESS_KEY")
    aws_session_token: Optional[str] = os.getenv("AWS_SESSION_TOKEN")

    # S3 Configuration
    s3_endpoint: Optional[str] = os.getenv(
        "S3_ENDPOINT")  # For LocalStack or custom S3
    s3_path_style_access: bool = os.getenv(
        "S3_PATH_STYLE_ACCESS", "false").lower() == "true"

    # Configuration Paths
    config_root_dir: str = os.getenv(
        "CONFIG_ROOT_DIR", "s3://your-config-bucket/configs")
    config_endpoints_dir: str = os.getenv(
        "CONFIG_ENDPOINTS_DIR", "s3://your-config-bucket/configs/endpoints")
    config_jobs_dir: str = os.getenv(
        "CONFIG_JOBS_DIR", "s3://your-config-bucket/configs/jobs")

    # JAR Configuration
    extra_jars_folder: str = os.getenv("EXTRA_JARS_FOLDER", "/opt/spark/jars")

    # Application Configuration
    app_name_prefix: str = os.getenv(
        "APP_NAME_PREFIX", "spark-metadata-pipeline")
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # EMR Configuration
    is_emr: bool = os.getenv("EMR_CLUSTER_ID") is not None
    emr_cluster_id: Optional[str] = os.getenv("EMR_CLUSTER_ID")

    # Database Configuration (for secrets)
    db_secrets_manager_enabled: bool = os.getenv(
        "DB_SECRETS_MANAGER_ENABLED", "false").lower() == "true"
    db_secrets_region: str = os.getenv("DB_SECRETS_REGION", aws_region)

    def get_s3_config(self) -> dict:
        """Get S3 configuration for Spark"""
        config = {}

        if self.s3_endpoint:
            config["spark.hadoop.fs.s3a.endpoint"] = self.s3_endpoint
            config["spark.hadoop.fs.s3a.path.style.access"] = "true"
        else:
            config["spark.hadoop.fs.s3a.path.style.access"] = str(
                self.s3_path_style_access).lower()

        if self.aws_access_key_id and self.aws_secret_access_key:
            config["spark.hadoop.fs.s3a.access.key"] = self.aws_access_key_id
            config["spark.hadoop.fs.s3a.secret.key"] = self.aws_secret_access_key

            if self.aws_session_token:
                config["spark.hadoop.fs.s3a.session.token"] = self.aws_session_token
                config["spark.hadoop.fs.s3a.aws.credentials.provider"] = "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider"
            else:
                config["spark.hadoop.fs.s3a.aws.credentials.provider"] = "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        elif self.is_emr:
            # Use IAM roles on EMR
            config["spark.hadoop.fs.s3a.aws.credentials.provider"] = "com.amazonaws.auth.InstanceProfileCredentialsProvider"

        return config

    def get_spark_config(self) -> dict:
        """Get base Spark configuration"""
        config = {
            "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
            "spark.scheduler.mode": "FAIR",
            "spark.sql.legacy.charVarcharAsString": "true",
            "spark.sql.adaptive.enabled": "true",
            "spark.sql.adaptive.coalescePartitions.enabled": "true",
            "spark.hadoop.fs.s3a.connection.timeout": "5000",
            "spark.hadoop.fs.s3a.attempts.maximum": "3",
            "spark.hadoop.fs.s3a.impl": "org.apache.hadoop.fs.s3a.S3AFileSystem",
            "mapreduce.filteroutputcommmitter.marksuccessfuljobs": "false",
            # Simple Parquet output - no complex catalog needed
        }

        # Add S3 configuration
        config.update(self.get_s3_config())

        # Add JAR configuration if not on EMR (EMR has jars pre-installed)
        if not self.is_emr and self.extra_jars_folder != "/opt/spark/jars":
            import os
            jar_dir = os.path.abspath(self.extra_jars_folder)

            # Explicitly list all JAR files for better compatibility
            jar_files = []
            if os.path.exists(jar_dir):
                for file in os.listdir(jar_dir):
                    if file.endswith('.jar'):
                        jar_files.append(os.path.join(jar_dir, file))

            if jar_files:
                jar_list = ','.join(jar_files)
                config["spark.jars"] = jar_list
                config["spark.driver.extraClassPath"] = jar_list
                config["spark.executor.extraClassPath"] = jar_list

        return config


# Global environment configuration instance
env_config = EnvironmentConfig()
