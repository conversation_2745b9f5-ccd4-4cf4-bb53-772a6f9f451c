from typing import TypeVar, Type
from pydantic import BaseModel, ValidationError
import yaml
import logging

T = TypeVar("T", bound=BaseModel)


def load_yaml(data_yaml: str, model: Type[T]):
    """Load YAML file and parse it into a given Pydantic model."""
    data = yaml.safe_load(data_yaml)

    if data is None:
        raise ValueError("YAML file is empty or contains only null values")

    try:
        model_data = model(**data)
        return model_data
    except ValidationError as e:
        logging.error(f"Validation error while parsing YAML: {e}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error happened: {e}")
        raise
