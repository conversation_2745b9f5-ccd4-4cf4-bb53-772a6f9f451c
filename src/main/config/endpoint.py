
from pydantic import BaseModel
from typing import Optional

class Credential(BaseModel):
    username: str
    password: str
    
class AwsCredential(BaseModel):
    access_key_id: str 
    secret_access_key: str 
    session_token: str


class AuthConfig(BaseModel):
    credential: Credential

class AwsAuthConfig(BaseModel):
    credential: AwsCredential

class BaseEndpoint(BaseModel):
    endpoint_type: str = ""

# Specific database endpoints inherit from BaseEndpoint
class MysqlEndpoint(BaseEndpoint):
    system: str
    host: str
    port: int
    schema_: str
    authentication: AuthConfig

class MssqlEndpoint(BaseEndpoint):
    system: str
    host: str
    port: int
    schema_: str
    authentication: AuthConfig
    database: str  # Mssql requires `database` field

class PostgresEndpoint(BaseEndpoint):
    system: str
    host: str
    port: int
    schema_: str
    authentication: AuthConfig
    database: str  # PostgreSQL requires `database` field

class InternalDBEndpoint(BaseEndpoint):
    system: str
    host: str
    port: int
    schema_: str
    authentication: AuthConfig
    database: str
    control_table: str
    system_name_column: str
    schema_name_column: str
    table_name_column: str


class S3Endpoint(BaseModel):
    bucket: str
    authentication: Optional[AwsAuthConfig] = None