
from config.reader import MysqlReaderConfig, MssqlReaderConfig, PostgresReaderConfig, S3ReaderConfig
from config.transformer import ProjectionTransformerConfig, WithColumnTransformerConfig
from config.writer import PostgresWriterConfig, S3WriterConfig, IcebergWriterConfig
from typing import Optional, Union, Annotated
from pydantic import BaseModel, Field


class PipelineConfig(BaseModel):
    name: str
    readers: Union[MssqlReaderConfig, MysqlReaderConfig,
                   PostgresReaderConfig, S3ReaderConfig] = Field(..., discriminator="type")
    transformers: Optional[list[Union[ProjectionTransformerConfig,
                                      WithColumnTransformerConfig]]] = None
    writers: Union[PostgresWriterConfig,
                   S3WriterConfig,
                   IcebergWriterConfig] = Field(..., discriminator="type")


class PipelineListConfig(BaseModel):
    jobs: list[PipelineConfig]
