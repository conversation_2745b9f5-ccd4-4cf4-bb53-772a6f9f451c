from utils import file_util
from config.pipeline import PipelineConfig, PipelineListConfig
from reader.factory_reader import ReaderFactory
from transformer.factory_transformer import TransfomerFactory
from writer.factory_writer import WriterFactory
from config.configparser import load_yaml
from utils.commons import JobParams, Pipeline
import os
import logging


class PipelineFactory:
    def __init__(self, params: JobParams):
        job_root_dir = f"{params.config_root_dir}/jobs"
        print(job_root_dir)
        file_path = f"{job_root_dir}/{params.config_file_path}"
        print(file_path)
        self.filtered_pipeline = self.parse_pipeline_config(
            file_path, params.pipeline_name)
        self.list_pipeline = []
        for pipeline in self.filtered_pipeline:
            logging.info(f"running job: {pipeline.name}")
            pipeline = self._create_pipeline(pipeline, params, params.custom)
            self.list_pipeline.append(pipeline)

    def _create_pipeline(self, config: PipelineConfig, params: JobParams, custom_options: dict[str, str]):
        process_date = params.report_date
        readers = ReaderFactory.create(
            config.readers, params, custom_options)  # type: ignore
        transformers = TransfomerFactory.create(
            config.transformers, custom_options)  # type: ignore
        writers = WriterFactory.create(
            config.writers, process_date, custom_options)         # type: ignore
        return Pipeline(readers, transformers, writers)  # type: ignore

    # function to filter only specific pipeline
    def parse_pipeline_config(self, config_path: str, specific_pipeline: list[str]):
        content = file_util.read_file(config_path)
        full_config = load_yaml(content, PipelineListConfig)
        list_job = []
        # check list empty
        if specific_pipeline:
            for job in full_config.jobs:
                # if job in specific
                if job.name in specific_pipeline:
                    list_job.append(job)
        else:
            list_job = full_config.jobs
        return list_job
