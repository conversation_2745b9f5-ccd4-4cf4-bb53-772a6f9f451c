
from writer.writer import Writer
from config.writer import PostgresWriterConfig
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import current_timestamp
from typing import Optional


class PostgreWriter(Writer):
     def __init__(self, config: PostgresWriterConfig, custom_options: Optional[dict[str,str]]):
        self.config = config
        self.custom_options = custom_options
        self.endpoint = config.endpoint
    
     def write(self, df: DataFrame):
         endpoint = self.config.endpoint_config
         cred = endpoint.authentication.credential # type: ignore
         url = f"jdbc:postgresql://{endpoint.host}/{endpoint.database}" # type: ignore
         pgwriter_options = {
             "driver":"org.postgresql.Driver",
             "sslMode": self.config.ssl_mode,
             "url": url,
             "dbtable": f"{endpoint.schema_}.{self.config.table}", # type: ignore
             "user": cred.username,
             "password": cred.password
         }
         if self.custom_options is not None:
             pgwriter_options.update(self.custom_options)
         finaldf = df.withColumn("_landing_loaded_at",current_timestamp())
         finaldf.write \
            .mode(self.config.mode) \
            .format("jdbc") \
            .options(**pgwriter_options) \
            .save()