
from writer.writer import Writer
from config.writer import S3WriterConfig,DSVWriterFormatConfig,AvroWriterFormatConfig,XMLWriterFormatConfig,ParquetWriterFormatConfig,ORCWriterFormatConfig,ExcelWriterFormatConfig,JsonlWriterFormatConfig
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import current_timestamp
from typing import Optional


class S3Writer(Writer):
     def __init__(self, config: S3WriterConfig, process_date, custom_options: Optional[dict[str,str]]):
        self.config = config
        self.custom_options = custom_options
        self.endpoint = config.endpoint
        self.process_date = process_date
    
     def write(self, df: DataFrame):                  
        endpoint_config = self.config.endpoint_config
        if self.custom_options is not None:
            key = self.custom_options.get("s3.writer.key",None) # type: ignore
            if key is None:
                key = self.config.key
        else:
            key = self.config.key # type: ignore
        path = endpoint_config.bucket # type: ignore
        options = self.config.format.source_options or dict()
         
        if isinstance(self.config.format,DSVWriterFormatConfig):
            source = "csv"
        elif isinstance(self.config.format,AvroWriterFormatConfig):
            source = "avro"
        elif isinstance(self.config.format,ParquetWriterFormatConfig):
            source = "parquet"
        elif isinstance(self.config.format,XMLWriterFormatConfig):
            source = "com.databricks.spark.xml"
        elif isinstance(self.config.format,ORCWriterFormatConfig):
            source = "orc"
        elif isinstance(self.config.format,ExcelWriterFormatConfig):
            source = "excel"
        elif isinstance(self.config.format,JsonlWriterFormatConfig):
            source = "json"
        else:
            raise ValueError(f"Unsupported type of file {self.config.format}")
        print(endpoint_config)
        # check if authentication is defined
        if endpoint_config.authentication is not None: # type: ignore
            creds = endpoint_config.authentication.credential # type: ignore
            hadoop_config = df.sparkSession.sparkContext._jsc.hadoopConfiguration()  # type: ignore
            hadoop_config.set("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
            hadoop_config.set("spark.hadoop.fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider")
            hadoop_config.set("spark.hadoop.fs.s3a.access.key", creds.access_key_id)
            hadoop_config.set("spark.hadoop.fs.s3a.secret.key", creds.secret_access_key)
            hadoop_config.set("spark.hadoop.fs.s3a.session.token", creds.session_token)
        path = f"{endpoint_config.bucket}/{key}" # type: ignore         
        partition_columns = self.config.partition_columns
        print(partition_columns)
        if partition_columns is not None:
            finaldf = df.repartition(self.config.num_files).write.format(source).mode(self.config.mode).options(**options).partitionBy(*partition_columns) # type: ignore
        else:     
            finaldf = df.repartition(self.config.num_files).write.format(source).mode(self.config.mode).options(**options) # type: ignore
        finaldf.save(f"s3a://{path}")