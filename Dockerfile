# Multi-stage build for Spark Metadata-Driven ETL Pipeline
FROM openjdk:11-jre-slim as base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    python3 \
    python3-pip \
    python3-venv \
    unixodbc \
    unixodbc-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Microsoft ODBC Driver for SQL Server
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql18 \
    && rm -rf /var/lib/apt/lists/*

# Set up Spark
ENV SPARK_VERSION=3.5.0
ENV HADOOP_VERSION=3
ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin

# Download and install Spark
RUN wget -q https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz \
    && tar -xzf spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz \
    && mv spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION} $SPARK_HOME \
    && rm spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz

# Create application directory
WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN python3 -m pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ ./src/
COPY scripts/ ./scripts/

# Create jars directory and download JAR files
RUN mkdir -p src/main/resources/jars

# Download required JAR files
RUN cd src/main/resources/jars && \
    # AWS SDK and Hadoop S3A support
    curl -L -O https://repo1.maven.org/maven2/org/apache/hadoop/hadoop-aws/3.3.4/hadoop-aws-3.3.4.jar && \
    curl -L -O https://repo1.maven.org/maven2/com/amazonaws/aws-java-sdk-bundle/1.12.262/aws-java-sdk-bundle-1.12.262.jar && \
    # Database drivers
    curl -L -O https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar && \
    curl -L -O https://repo1.maven.org/maven2/org/postgresql/postgresql/42.6.0/postgresql-42.6.0.jar && \
    curl -L -O https://repo1.maven.org/maven2/com/microsoft/sqlserver/mssql-jdbc/12.4.1.jre11/mssql-jdbc-12.4.1.jre11.jar

# Set environment variables for the application
ENV PYTHONPATH=/app/src/main
ENV CONFIG_ROOT_DIR=/app/configs
ENV EXTRA_JARS_FOLDER=/app/src/main/resources/jars
ENV APP_NAME_PREFIX=spark-metadata-pipeline-docker

# Create directories for configurations and outputs
RUN mkdir -p /app/configs/jobs /app/configs/endpoints /app/outputs

# Copy default configuration files
COPY src/main/resources/ ./configs/

# Set default environment variables (can be overridden)
ENV DB_TYPE=mysql
ENV DB_HOST=mysql
ENV DB_PORT=3306
ENV DB_NAME=test
ENV DB_USER=test
ENV DB_PASSWORD=123456
ENV DB_SCHEMA=test

# SQL Server specific defaults
ENV MSSQL_HOST=sqlserver
ENV MSSQL_PORT=1433
ENV MSSQL_DATABASE=master
ENV MSSQL_USER=sa
ENV MSSQL_PASSWORD=123456aA
ENV MSSQL_SCHEMA=dbo

# PostgreSQL specific defaults
ENV POSTGRES_HOST=postgres
ENV POSTGRES_PORT=5432
ENV POSTGRES_DATABASE=test
ENV POSTGRES_USER=postgres
ENV POSTGRES_PASSWORD=123456
ENV POSTGRES_SCHEMA=public

# S3/Output configuration
ENV S3_ENDPOINT=http://localstack:4566
ENV S3_PATH_STYLE_ACCESS=true
ENV OUTPUT_PATH=/app/outputs

# Expose port for Spark UI (optional)
EXPOSE 4040

# Create entrypoint script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "🚀 Starting Spark Metadata-Driven ETL Pipeline"\n\
echo "Database Type: $DB_TYPE"\n\
echo "Configuration: $CONFIG_ROOT_DIR"\n\
echo "Output Path: $OUTPUT_PATH"\n\
echo ""\n\
\n\
# Set PYTHONPATH\n\
export PYTHONPATH=/app/src/main\n\
\n\
# Change to source directory\n\
cd /app/src/main\n\
\n\
# Run the pipeline with provided arguments\n\
python3 pipeline_main.py "$@"\n\
' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Default command (can be overridden)
CMD ["--config_file", "sample_job.yaml", "--app_type", "batch", "--config_root_dir", "/app/configs", "--s3_type", "localstack", "--extra_jars_folder", "/app/src/main/resources/jars"]
