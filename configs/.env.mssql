# SQL Server Database Configuration
DB_TYPE=mssql
DB_HOST=sqlserver
DB_PORT=1433
DB_NAME=master
DB_USER=sa
DB_PASSWORD=123456aA
DB_SCHEMA=dbo

# SQL Server specific settings
MSSQL_HOST=sqlserver
MSSQL_PORT=1433
MSSQL_DATABASE=master
MSSQL_USER=sa
MSSQL_PASSWORD=123456aA
MSSQL_SCHEMA=dbo

# S3/LocalStack Configuration
S3_ENDPOINT=http://localstack:4566
S3_PATH_STYLE_ACCESS=true
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_DEFAULT_REGION=us-east-1

# Pipeline Configuration
CONFIG_ROOT_DIR=/app/configs
EXTRA_JARS_FOLDER=/app/src/main/resources/jars
APP_NAME_PREFIX=spark-metadata-pipeline-mssql
LOG_LEVEL=INFO

# Output Configuration
OUTPUT_PATH=/app/outputs
