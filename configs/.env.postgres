# PostgreSQL Database Configuration
DB_TYPE=postgres
DB_HOST=postgres
DB_PORT=5432
DB_NAME=test
DB_USER=postgres
DB_PASSWORD=123456
DB_SCHEMA=public

# PostgreSQL specific settings
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DATABASE=test
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
POSTGRES_SCHEMA=public

# S3/LocalStack Configuration
S3_ENDPOINT=http://localstack:4566
S3_PATH_STYLE_ACCESS=true
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_DEFAULT_REGION=us-east-1

# Pipeline Configuration
CONFIG_ROOT_DIR=/app/configs
EXTRA_JARS_FOLDER=/app/src/main/resources/jars
APP_NAME_PREFIX=spark-metadata-pipeline-postgres
LOG_LEVEL=INFO

# Output Configuration
OUTPUT_PATH=/app/outputs
