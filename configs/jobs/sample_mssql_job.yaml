jobs:
  - name: mssql_to_parquet_job
    readers:
      type: MssqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      strategy: full
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: processed_at
             expr: current_timestamp()
           - colname: source_system
             expr: "'mssql'"
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - processed_at
           - source_system
    writers:
      type: S3Writer
      endpoint: s3.localstack.endpoint.yaml
      key: outputs/mssql_data
      mode: overwrite
      num_files: 1
      format: 
        type: ParquetWriterFormat
