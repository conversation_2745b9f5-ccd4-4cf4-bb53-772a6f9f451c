# MySQL Database Configuration
DB_TYPE=mysql
DB_HOST=mysql
DB_PORT=3306
DB_NAME=test
DB_USER=test
DB_PASSWORD=123456
DB_SCHEMA=test

# S3/LocalStack Configuration
S3_ENDPOINT=http://localstack:4566
S3_PATH_STYLE_ACCESS=true
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_DEFAULT_REGION=us-east-1

# Pipeline Configuration
CONFIG_ROOT_DIR=/app/configs
EXTRA_JARS_FOLDER=/app/src/main/resources/jars
APP_NAME_PREFIX=spark-metadata-pipeline-mysql
LOG_LEVEL=INFO

# Output Configuration
OUTPUT_PATH=/app/outputs
