version: '3.7'
networks:
  mynetwork:
    driver: bridge

services:
  postgres:
    container_name: postgres
    image: postgres:16
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: test
    restart: always
    networks:
      - mynetwork
    ports:
      - "5432:5432"

  mysql:
    image: mysql:8.0.41
    container_name: mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: test
      MYSQL_USER: test
      MYSQL_PASSWORD: 123456
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - mynetwork

  sqlserver:
    image: mcr.microsoft.com/mssql/server:latest
    container_name: sqlserver
    platform: linux/amd64 # Force x86 emulation
    restart: always
    environment:
      SA_PASSWORD: "123456aA"
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    volumes:
      - sqlserver-data:/var/opt/mssql

  localstack:
    image: localstack/localstack:latest
    container_name: localstack
    ports:
      - "4566:4566" # main entry point for all services
    environment:
      - SERVICES=s3,lambda,dynamodb,cloudwatch,sqs,sns
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - ./localstack:/var/lib/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - mynetwork

  # Spark ETL Pipeline Service
  spark-pipeline:
    build: .
    container_name: spark-pipeline
    depends_on:
      - mysql
      - sqlserver
      - postgres
      - localstack
    networks:
      - mynetwork
    volumes:
      - ./configs:/app/configs
      - ./outputs:/app/outputs
    environment:
      # Default to MySQL configuration
      - DB_TYPE=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=test
      - DB_USER=test
      - DB_PASSWORD=123456
      - DB_SCHEMA=test

      # SQL Server configuration (when DB_TYPE=mssql)
      - MSSQL_HOST=sqlserver
      - MSSQL_PORT=1433
      - MSSQL_DATABASE=master
      - MSSQL_USER=sa
      - MSSQL_PASSWORD=123456aA
      - MSSQL_SCHEMA=dbo

      # PostgreSQL configuration (when DB_TYPE=postgres)
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DATABASE=test
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=123456
      - POSTGRES_SCHEMA=public

      # S3/LocalStack configuration
      - S3_ENDPOINT=http://localstack:4566
      - S3_PATH_STYLE_ACCESS=true
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=us-east-1

      # Pipeline configuration
      - CONFIG_ROOT_DIR=/app/configs
      - EXTRA_JARS_FOLDER=/app/src/main/resources/jars
      - APP_NAME_PREFIX=spark-metadata-pipeline-docker
    # Override the default command to wait for user input
    # Remove this line to run automatically, or customize the command
    command: ["--help"]

volumes:
  mysql-data:
  sqlserver-data:
