version: '3.7'
networks:
  mynetwork:
    driver: bridge

services:
  postgres:
    container_name: postgres
    image: postgres:16
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: test
    restart: always
    networks:
      - mynetwork
    ports:
      - "5432:5432"

  mysql:
    image: mysql:8.0.41
    container_name: mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: test
      MYSQL_USER: test
      MYSQL_PASSWORD: 123456
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - mynetwork

  sqlserver:
    image: mcr.microsoft.com/mssql/server:latest
    container_name: sqlserver
    platform: linux/amd64 # Force x86 emulation
    restart: always
    environment:
      SA_PASSWORD: "123456aA"
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    volumes:
      - sqlserver-data:/var/opt/mssql

  localstack:
    image: localstack/localstack:latest
    container_name: localstack
    ports:
      - "4566:4566" # main entry point for all services
    environment:
      - SERVICES=s3,lambda,dynamodb,cloudwatch,sqs,sns
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - ./localstack:/var/lib/localstack
      - /var/run/docker.sock:/var/run/docker.sock
volumes:
  mysql-data:
  sqlserver-data:
